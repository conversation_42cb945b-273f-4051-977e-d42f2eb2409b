#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_crypto_ticker/esp32_crypto_ticker.ino"
#include <Arduino.h>
#include <WiFi.h>
#include <HTTPClient.h>
#include <ArduinoJson.h>
#include <ESP32-HUB75-MatrixPanel-I2S-DMA.h>
#include <Adafruit_GFX.h>
#include <PubSubClient.h>
#include "CryptoDisplay.h"

// WiFi Configuration
#ifndef WIFI_SSID
#define WIFI_SSID "your_wifi_ssid"
#endif
#ifndef WIFI_PASSWORD
#define WIFI_PASSWORD "your_wifi_password"
#endif

// MQTT Configuration (Optional)
#ifndef MQTT_HOST
#define MQTT_HOST "*************"
#endif
#ifndef MQTT_PORT
#define MQTT_PORT 1883
#endif
#ifndef MQTT_USER
#define MQTT_USER ""
#endif
#ifndef MQTT_PASS
#define MQTT_PASS ""
#endif

// MQTT Topics
static const char* TOPIC_BRIGHTNESS = "crypto/brightness";
static const char* TOPIC_POWER = "crypto/power";
static const char* TOPIC_CURRENCY = "crypto/currency";
static const char* TOPIC_SCROLL_DIR = "crypto/scroll_direction";
static const char* TOPIC_STATUS = "crypto/status";

// Device Configuration
#ifndef DEVICE_ID
#define DEVICE_ID "esp32-crypto-ticker"
#endif

// Panel Configuration
#define PANEL_RES_X 64
#define PANEL_RES_Y 32
#define PANEL_CHAIN 1
#define DEFAULT_BRIGHTNESS 80

// ---------------------------
// 🔌 Pin Definitions (ESP32-S3 to HUB75 RGB Matrix)
// ---------------------------
#define R1_PIN 47
#define G1_PIN 1
#define B1_PIN 48
#define R2_PIN 45
#define G2_PIN 2
#define B2_PIN 0  // ⚠️ GPIO0 is BOOT pin, must stay HIGH at boot

#define A_PIN 35
#define B_PIN 41
#define C_PIN 36
#define D_PIN 40
#define E_PIN 42

#define LAT_PIN 39
#define OE_PIN 38
#define CLK_PIN 37

// Crypto API Configuration
#define COINGECKO_API "https://api.coingecko.com/api/v3/"
#define UPDATE_INTERVAL 60000  // 60 seconds
#define DISPLAY_UPDATE_INTERVAL 5000  // 5 seconds for display cycling

// Time Configuration
#define TZ_EUROPE_DUBLIN "GMT0IST,M3.5.0/1,M10.5.0/2"

// Global Objects
MatrixPanel_I2S_DMA* dma_display = nullptr;
HTTPClient http;
CryptoDisplay cryptoDisplay;
WiFiClient wifiClient;
PubSubClient mqttClient(wifiClient);

// Global State
String cryptoCurrencies[] = {"bitcoin", "ethereum", "cardano", "solana"};
int numCurrencies = 4;
int currentCurrency = 0;
unsigned long lastUpdateTime = 0;
unsigned long lastDisplayTime = 0;
int currentBrightness = DEFAULT_BRIGHTNESS;
bool displayPower = true;
String fiatCurrency = "usd";
String scrollDirection = "left";
bool showSparkline = true;
bool showRank = true;
bool showVolume = true;
bool mqttEnabled = false;

// Forward Declarations
void setupDisplay();
void setupWiFi();
void setupTime();
void setupMQTT();
void updateCryptoData();
void displayCrypto();
void cycleCurrency();
void mqttCallback(char* topic, byte* payload, unsigned int length);
void mqttEnsureConnected();

#line 111 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_crypto_ticker/esp32_crypto_ticker.ino"
void setup();
#line 151 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_crypto_ticker/esp32_crypto_ticker.ino"
void loop();
#line 373 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_crypto_ticker/esp32_crypto_ticker.ino"
void publishMQTTStatus();
#line 111 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_crypto_ticker/esp32_crypto_ticker.ino"
void setup() {
  Serial.begin(115200);
  Serial.println("ESP32 Crypto Ticker Starting...");

  // Setup hardware
  setupDisplay();
  setupWiFi();
  setupTime();
  
  // Setup MQTT if enabled
  #ifdef MQTT_HOST
  if (strlen(MQTT_HOST) > 0) {
    mqttEnabled = true;
    setupMQTT();
  }
  #endif

  // Initialize crypto display with settings
  cryptoDisplay.init(dma_display);
  cryptoDisplay.setFiatCurrency(fiatCurrency);
  cryptoDisplay.setScrollDirection(scrollDirection);
  cryptoDisplay.setShowSparkline(showSparkline);
  cryptoDisplay.setShowRank(showRank);
  cryptoDisplay.setShowVolume(showVolume);

  Serial.println("Setup complete!");
  Serial.print("IP Address: ");
  Serial.println(WiFi.localIP());

  // Show welcome message
  dma_display->clearScreen();
  dma_display->setTextColor(dma_display->color565(0, 255, 0));
  dma_display->setTextSize(1);
  dma_display->setCursor(2, 8);
  dma_display->print("Crypto Ticker");
  dma_display->setCursor(2, 18);
  dma_display->print("Starting...");
  delay(2000);
}

void loop() {
  unsigned long currentTime = millis();
  
  // Handle WiFi reconnection
  if (WiFi.status() != WL_CONNECTED) {
    setupWiFi();
  }

  // Handle MQTT if enabled
  if (mqttEnabled) {
    mqttEnsureConnected();
    mqttClient.loop();
  }

  // Update crypto data periodically
  if (currentTime - lastUpdateTime >= UPDATE_INTERVAL) {
    updateCryptoData();
    lastUpdateTime = currentTime;
  }

  // Update display periodically
  if (currentTime - lastDisplayTime >= DISPLAY_UPDATE_INTERVAL) {
    displayCrypto();
    cycleCurrency();
    lastDisplayTime = currentTime;
  }

  // Update display rendering
  if (displayPower) {
    cryptoDisplay.update();
  }

  delay(10);
}

void setupDisplay() {
  HUB75_I2S_CFG mxconfig(PANEL_RES_X, PANEL_RES_Y, PANEL_CHAIN);

  mxconfig.gpio.r1 = R1_PIN;
  mxconfig.gpio.g1 = G1_PIN;
  mxconfig.gpio.b1 = B1_PIN;
  mxconfig.gpio.r2 = R2_PIN;
  mxconfig.gpio.g2 = G2_PIN;
  mxconfig.gpio.b2 = B2_PIN;

  mxconfig.gpio.a = A_PIN;
  mxconfig.gpio.b = B_PIN;
  mxconfig.gpio.c = C_PIN;
  mxconfig.gpio.d = D_PIN;
  mxconfig.gpio.e = E_PIN;

  mxconfig.gpio.lat = LAT_PIN;
  mxconfig.gpio.oe = OE_PIN;
  mxconfig.gpio.clk = CLK_PIN;

  mxconfig.clkphase = false;
  mxconfig.driver = HUB75_I2S_CFG::FM6124;

  dma_display = new MatrixPanel_I2S_DMA(mxconfig);
  dma_display->begin();
  dma_display->setBrightness8(DEFAULT_BRIGHTNESS);
  currentBrightness = DEFAULT_BRIGHTNESS;
  dma_display->clearScreen();
}

void setupWiFi() {
  WiFi.mode(WIFI_STA);
  WiFi.begin(WIFI_SSID, WIFI_PASSWORD);
  
  Serial.print("Connecting to WiFi");
  while (WiFi.status() != WL_CONNECTED) {
    delay(500);
    Serial.print(".");
  }
  Serial.println();
  Serial.println("WiFi connected!");
}

void setupTime() {
  configTzTime(TZ_EUROPE_DUBLIN, "pool.ntp.org", "time.nist.gov");
  
  time_t now = 0;
  int retry = 0;
  while (now < 100000 && retry < 20) {
    delay(250);
    now = time(nullptr);
    retry++;
  }
  Serial.println("Time synchronized");
}

void updateCryptoData() {
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("WiFi not connected, skipping update");
    cryptoDisplay.drawErrorScreen("No WiFi");
    return;
  }

  String coinId = cryptoCurrencies[currentCurrency];
  String url = String(COINGECKO_API) + "coins/" + coinId + "?tickers=false&market_data=true&community_data=false&developer_data=false&sparkline=false";
  
  http.begin(url);
  http.addHeader("User-Agent", "ESP32-Crypto-Ticker/1.0");
  
  int httpResponseCode = http.GET();
  
  if (httpResponseCode == 200) {
    String payload = http.getString();
    Serial.println("API Response received");
    
    DynamicJsonDocument doc(4096);
    DeserializationError error = deserializeJson(doc, payload);
    
    if (!error) {
      // Extract comprehensive data
      String name = doc["name"];
      String symbol = doc["symbol"];
      float price = doc["market_data"]["current_price"]["usd"];
      float change24h = doc["market_data"]["price_change_percentage_24h"];
      float volume = doc["market_data"]["total_volume"]["usd"];
      int rank = doc["market_cap_rank"] | 0;
      float ath = doc["market_data"]["ath"]["usd"];
      
      cryptoDisplay.setCryptoData(coinId, price, change24h, volume, rank, ath);
      Serial.printf("Updated %s: $%.2f (%.2f%%) Vol:%.0f Rank:#%d ATH:$%.2f\n", 
                    coinId.c_str(), price, change24h, volume, rank, ath);
    } else {
      Serial.println("JSON parsing failed: " + String(error.c_str()));
      cryptoDisplay.drawErrorScreen("Parse Error");
    }
  } else {
    Serial.printf("HTTP request failed: %d\n", httpResponseCode);
    cryptoDisplay.drawErrorScreen("API Error " + String(httpResponseCode));
  }
  
  http.end();
}

void displayCrypto() {
  if (displayPower) {
    cryptoDisplay.displayCurrentCrypto();
  }
}

void cycleCurrency() {
  currentCurrency = (currentCurrency + 1) % numCurrencies;
  Serial.printf("Cycling to currency: %s\n", cryptoCurrencies[currentCurrency].c_str());
}

void setupMQTT() {
  mqttClient.setServer(MQTT_HOST, MQTT_PORT);
  mqttClient.setCallback(mqttCallback);
  mqttEnsureConnected();
}

void mqttEnsureConnected() {
  if (!mqttClient.connected()) {
    Serial.print("Connecting to MQTT...");
    if (mqttClient.connect(DEVICE_ID, MQTT_USER, MQTT_PASS, 
                          TOPIC_STATUS, 0, true, "offline")) {
      Serial.println(" connected!");
      
      // Publish online status
      mqttClient.publish(TOPIC_STATUS, "online", true);
      
      // Subscribe to control topics
      mqttClient.subscribe(TOPIC_BRIGHTNESS);
      mqttClient.subscribe(TOPIC_POWER);
      mqttClient.subscribe(TOPIC_CURRENCY);
      mqttClient.subscribe(TOPIC_SCROLL_DIR);
      
      // Publish current status
      publishMQTTStatus();
    } else {
      Serial.print(" failed, rc=");
      Serial.println(mqttClient.state());
    }
  }
}

void mqttCallback(char* topic, byte* payload, unsigned int length) {
  char message[length + 1];
  memcpy(message, payload, length);
  message[length] = '\0';
  
  String msg = String(message);
  msg.trim();

  Serial.printf("MQTT: %s -> %s\n", topic, msg.c_str());

  if (strcmp(topic, TOPIC_BRIGHTNESS) == 0) {
    int brightness = msg.toInt();
    brightness = constrain(brightness, 0, 255);
    currentBrightness = brightness;
    dma_display->setBrightness8(brightness);
    cryptoDisplay.setBrightness(brightness);
  }
  else if (strcmp(topic, TOPIC_POWER) == 0) {
    displayPower = (msg.equalsIgnoreCase("on") || msg.equals("1"));
    cryptoDisplay.setPower(displayPower);
    if (!displayPower) {
      dma_display->clearScreen();
    }
  }
  else if (strcmp(topic, TOPIC_CURRENCY) == 0) {
    // Find currency in array and switch to it
    for (int i = 0; i < numCurrencies; i++) {
      if (cryptoCurrencies[i] == msg) {
        currentCurrency = i;
        Serial.printf("Switched to currency: %s\n", msg.c_str());
        break;
      }
    }
  }
  else if (strcmp(topic, TOPIC_SCROLL_DIR) == 0) {
    if (msg == "left" || msg == "right" || msg == "up" || msg == "down" || msg == "none") {
      scrollDirection = msg;
      cryptoDisplay.setScrollDirection(scrollDirection);
    }
  }
}

void publishMQTTStatus() {
  if (!mqttClient.connected()) return;
  
  String status = "{";
  status += "\"currency\":\"" + cryptoCurrencies[currentCurrency] + "\",";
  status += "\"power\":" + String(displayPower ? "true" : "false") + ",";
  status += "\"brightness\":" + String(currentBrightness) + ",";
  status += "\"scroll_direction\":\"" + scrollDirection + "\",";
  status += "\"fiat\":\"" + fiatCurrency + "\",";
  status += "\"sparkline\":" + String(showSparkline ? "true" : "false") + ",";
  status += "\"rank\":" + String(showRank ? "true" : "false") + ",";
  status += "\"volume\":" + String(showVolume ? "true" : "false") + ",";
  status += "\"ip\":\"" + WiFi.localIP().toString() + "\"";
  status += "}";
  
  mqttClient.publish(TOPIC_STATUS, status.c_str(), true);
}
