#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_crypto_ticker/CryptoDisplay.cpp"
#include "CryptoDisplay.h"

CryptoDisplay::CryptoDisplay() 
  : disp(nullptr), scrollX(64), scrollY(0), lastScrollUpdate(0), lastDisplayUpdate(0), 
    showClock(true), displayPower(true), scrollDirection("left"), fiatCurrency("usd"), 
    fiatSymbol("$"), showSparkline(true), showRank(true), showVolume(true) {
  currentCrypto.isValid = false;
  currentCrypto.historyIndex = 0;
  currentCrypto.hasHistory = false;
  scrollText = "Connecting...";
  
  // Initialize price history array
  for(int i = 0; i < 24; i++) {
    currentCrypto.priceHistory[i] = 0.0;
  }
}

void CryptoDisplay::init(MatrixPanel_I2S_DMA* display) {
  disp = display;
  scrollX = disp->width();
}

void CryptoDisplay::setCryptoData(const String& coinId, float price, float change24h, float volume, int rank, float ath) {
  currentCrypto.name = coinId;
  currentCrypto.symbol = getCryptoSymbol(coinId);
  currentCrypto.price = price;
  currentCrypto.change24h = change24h;
  currentCrypto.volume = volume;
  currentCrypto.marketCapRank = rank;
  currentCrypto.ath = ath;
  currentCrypto.isAtATH = (price >= ath * 0.99); // Within 1% of ATH
  currentCrypto.isValid = true;
  currentCrypto.lastUpdate = millis();
  
  // Update price history for sparkline
  updatePriceHistory(price);
  
  // Update scroll text with crypto info
  String changeStr = (change24h >= 0 ? "+" : "") + String(change24h, 2) + "%";
  String volumeStr = showVolume ? " Vol:" + formatVolume(volume) : "";
  String rankStr = showRank && rank > 0 ? " #" + String(rank) : "";
  String athStr = currentCrypto.isAtATH ? " ATH!" : "";
  
  scrollText = currentCrypto.symbol + " " + fiatSymbol + formatPrice(price) + " " + changeStr + rankStr + volumeStr + athStr;
}

void CryptoDisplay::displayCurrentCrypto() {
  if (!disp || !displayPower) return;
  
  disp->clearScreen();
  
  if (currentCrypto.isValid) {
    drawCryptoInfo();
  } else {
    // Show loading message
    disp->setTextColor(colorFromRGB(255, 255, 0));
    disp->setTextSize(1);
    disp->setCursor(2, 12);
    disp->print("Loading...");
  }
  
  // Draw clock at top if enabled
  if (showClock) {
    drawClock();
  }
}

void CryptoDisplay::update() {
  unsigned long now = millis();
  
  // Update scrolling text based on direction
  if (now - lastScrollUpdate >= 50) { // 50ms for smooth scrolling
    if (scrollDirection == "left") {
      scrollLeft();
    } else if (scrollDirection == "right") {
      scrollRight();
    } else if (scrollDirection == "up") {
      scrollUp();
    } else if (scrollDirection == "down") {
      scrollDown();
    }
    lastScrollUpdate = now;
  }
  
  // Redraw display periodically
  if (now - lastDisplayUpdate >= 100) {
    if (displayPower && currentCrypto.isValid) {
      displayCurrentCrypto();
    }
    lastDisplayUpdate = now;
  }
}

void CryptoDisplay::drawCryptoInfo() {
  if (!currentCrypto.isValid) return;
  
  // Draw crypto icon (top left, simple symbol-based representation)
  drawCryptoIcon(currentCrypto.name);
  
  // Draw crypto symbol (next to icon)
  disp->setTextSize(1);
  disp->setCursor(16, 0);
  disp->setTextColor(colorFromRGB(0, 255, 255));
  disp->print(currentCrypto.symbol);
  
  // Draw rank if enabled
  if (showRank && currentCrypto.marketCapRank > 0) {
    String rankStr = "#" + String(currentCrypto.marketCapRank);
    disp->setCursor(disp->width() - rankStr.length() * 6, 0);
    disp->setTextColor(colorFromRGB(255, 165, 0));
    disp->print(rankStr);
  }
  
  // Draw ATH indicator if at all-time high
  if (currentCrypto.isAtATH) {
    disp->setCursor(disp->width() - 18, 8);
    disp->setTextColor(colorFromRGB(255, 215, 0));
    disp->print("ATH");
  }
  
  // Draw price (center, large)
  disp->setTextSize(1);
  String priceStr = fiatSymbol + formatPrice(currentCrypto.price);
  int textWidth = priceStr.length() * 6;
  int x = (disp->width() - textWidth) / 2;
  disp->setCursor(x, 12);
  disp->setTextColor(colorFromRGB(255, 255, 255));
  disp->print(priceStr);
  
  // Draw sparkline if enabled
  if (showSparkline && currentCrypto.hasHistory) {
    drawSparkline();
  }
  
  // Draw 24h change (bottom left)
  String changeStr = (currentCrypto.change24h >= 0 ? "+" : "") + String(currentCrypto.change24h, 1) + "%";
  disp->setCursor(0, 24);
  disp->setTextColor(getPriceChangeColor(currentCrypto.change24h));
  disp->print(changeStr);
  
  // Draw volume if enabled (bottom right)
  if (showVolume && currentCrypto.volume > 0) {
    String volStr = formatVolume(currentCrypto.volume);
    disp->setCursor(disp->width() - volStr.length() * 6, 24);
    disp->setTextColor(colorFromRGB(150, 150, 150));
    disp->print(volStr);
  }
}

void CryptoDisplay::drawScrollingText() {
  if (!displayPower) return;
  
  // Clear bottom area for scrolling text
  disp->fillRect(0, 20, disp->width(), 12, colorFromRGB(0, 0, 0));
  
  // Draw scrolling text
  disp->setTextSize(1);
  disp->setCursor(scrollX, 22);
  disp->setTextColor(colorFromRGB(255, 255, 0));
  disp->print(scrollText);
}

void CryptoDisplay::drawClock() {
  time_t t = time(nullptr);
  struct tm timeinfo;
  if (!localtime_r(&t, &timeinfo)) return;
  
  // Draw time at top right
  char timeStr[9];
  snprintf(timeStr, sizeof(timeStr), "%02d:%02d", timeinfo.tm_hour, timeinfo.tm_min);
  
  int textWidth = strlen(timeStr) * 6;
  int x = disp->width() - textWidth;
  disp->setCursor(x, 0);
  disp->setTextColor(colorFromRGB(255, 100, 100));
  disp->setTextSize(1);
  disp->print(timeStr);
}

void CryptoDisplay::setScrollText(const String& text) {
  scrollText = text;
  scrollX = disp->width();
}

void CryptoDisplay::setBrightness(int brightness) {
  if (disp) {
    disp->setBrightness8(constrain(brightness, 0, 255));
  }
}

void CryptoDisplay::setPower(bool power) {
  displayPower = power;
  if (!power && disp) {
    disp->clearScreen();
  }
}

uint16_t CryptoDisplay::colorFromRGB(uint8_t r, uint8_t g, uint8_t b) {
  return disp->color565(r, g, b);
}

String CryptoDisplay::formatPrice(float price) {
  if (price >= 1000) {
    return String((int)price);
  } else if (price >= 1) {
    return String(price, 2);
  } else if (price >= 0.01) {
    return String(price, 4);
  } else {
    return String(price, 6);
  }
}

String CryptoDisplay::getCryptoSymbol(const String& coinId) {
  if (coinId == "bitcoin") return "BTC";
  if (coinId == "ethereum") return "ETH";
  if (coinId == "cardano") return "ADA";
  if (coinId == "solana") return "SOL";
  if (coinId == "chainlink") return "LINK";
  if (coinId == "polkadot") return "DOT";
  if (coinId == "dogecoin") return "DOGE";
  if (coinId == "shiba-inu") return "SHIB";
  if (coinId == "avalanche-2") return "AVAX";
  if (coinId == "polygon") return "MATIC";
  return coinId.substring(0, 4).toUpperCase();
}

uint16_t CryptoDisplay::getPriceChangeColor(float change) {
  if (change > 0) {
    return colorFromRGB(0, 255, 0); // Green for positive
  } else if (change < 0) {
    return colorFromRGB(255, 0, 0); // Red for negative
  } else {
    return colorFromRGB(255, 255, 255); // White for no change
  }
}

void CryptoDisplay::drawSparkline() {
  if (!currentCrypto.hasHistory) return;
  
  // Find min and max for scaling
  float minPrice = currentCrypto.priceHistory[0];
  float maxPrice = currentCrypto.priceHistory[0];
  
  for (int i = 0; i < 24; i++) {
    if (currentCrypto.priceHistory[i] > 0) {
      if (currentCrypto.priceHistory[i] < minPrice || minPrice == 0) minPrice = currentCrypto.priceHistory[i];
      if (currentCrypto.priceHistory[i] > maxPrice) maxPrice = currentCrypto.priceHistory[i];
    }
  }
  
  if (maxPrice == minPrice) return; // No variation to show
  
  // Draw sparkline in bottom area
  int sparklineY = 20;
  int sparklineHeight = 8;
  int sparklineWidth = min(24, disp->width() - 4);
  
  uint16_t sparkColor = getPriceChangeColor(currentCrypto.change24h);
  
  for (int i = 1; i < sparklineWidth && i < 24; i++) {
    if (currentCrypto.priceHistory[i] > 0 && currentCrypto.priceHistory[i-1] > 0) {
      int y1 = sparklineY + sparklineHeight - ((currentCrypto.priceHistory[i-1] - minPrice) / (maxPrice - minPrice)) * sparklineHeight;
      int y2 = sparklineY + sparklineHeight - ((currentCrypto.priceHistory[i] - minPrice) / (maxPrice - minPrice)) * sparklineHeight;
      
      disp->drawLine(i-1 + 2, y1, i + 2, y2, sparkColor);
    }
  }
  
  // Draw current price point
  if (currentCrypto.price > 0) {
    int currentY = sparklineY + sparklineHeight - ((currentCrypto.price - minPrice) / (maxPrice - minPrice)) * sparklineHeight;
    disp->drawCircle(sparklineWidth + 1, currentY, 1, colorFromRGB(255, 255, 0));
  }
}

void CryptoDisplay::updatePriceHistory(float price) {
  currentCrypto.priceHistory[currentCrypto.historyIndex] = price;
  currentCrypto.historyIndex = (currentCrypto.historyIndex + 1) % 24;
  
  // Mark as having history after first full cycle
  if (currentCrypto.historyIndex == 0) {
    currentCrypto.hasHistory = true;
  }
}

String CryptoDisplay::formatVolume(float volume) {
  if (volume >= 1000000000) {
    return String(volume / 1000000000, 1) + "B";
  } else if (volume >= 1000000) {
    return String(volume / 1000000, 1) + "M";
  } else if (volume >= 1000) {
    return String(volume / 1000, 1) + "K";
  } else {
    return String((int)volume);
  }
}

String CryptoDisplay::getFiatSymbol(const String& currency) {
  if (currency == "usd") return "$";
  if (currency == "eur") return "€";
  if (currency == "gbp") return "£";
  if (currency == "jpy" || currency == "cny") return "¥";
  if (currency == "btc") return "₿";
  return "$"; // Default to USD
}

void CryptoDisplay::setScrollDirection(const String& direction) {
  scrollDirection = direction;
  // Reset scroll position when direction changes
  scrollX = disp->width();
  scrollY = 0;
}

void CryptoDisplay::setFiatCurrency(const String& currency) {
  fiatCurrency = currency;
  fiatSymbol = getFiatSymbol(currency);
}

void CryptoDisplay::setShowSparkline(bool show) {
  showSparkline = show;
}

void CryptoDisplay::setShowRank(bool show) {
  showRank = show;
}

void CryptoDisplay::setShowVolume(bool show) {
  showVolume = show;
}

void CryptoDisplay::scrollLeft() {
  scrollX--;
  if (scrollX < -(int)(scrollText.length() * 6)) {
    scrollX = disp->width();
  }
}

void CryptoDisplay::scrollRight() {
  scrollX++;
  if (scrollX > disp->width()) {
    scrollX = -(int)(scrollText.length() * 6);
  }
}

void CryptoDisplay::scrollUp() {
  scrollY--;
  if (scrollY < -8) {
    scrollY = disp->height();
  }
}

void CryptoDisplay::scrollDown() {
  scrollY++;
  if (scrollY > disp->height()) {
    scrollY = -8;
  }
}

void CryptoDisplay::drawErrorScreen(const String& message) {
  disp->clearScreen();
  
  // Draw error icon (simple X)
  disp->drawLine(disp->width()/2 - 4, 4, disp->width()/2 + 4, 12, colorFromRGB(255, 0, 0));
  disp->drawLine(disp->width()/2 + 4, 4, disp->width()/2 - 4, 12, colorFromRGB(255, 0, 0));
  
  // Draw error message
  disp->setTextSize(1);
  disp->setCursor(0, 16);
  disp->setTextColor(colorFromRGB(255, 0, 0));
  disp->print("Error:");
  
  // Wrap text if too long
  int maxChars = disp->width() / 6;
  String wrappedMessage = message;
  if (message.length() > maxChars) {
    wrappedMessage = message.substring(0, maxChars - 3) + "...";
  }
  
  disp->setCursor(0, 24);
  disp->setTextColor(colorFromRGB(255, 255, 255));
  disp->print(wrappedMessage);
}

// Simple pixel-art style crypto icons
void CryptoDisplay::drawCryptoIcon(const String& coinId) {
  uint16_t color = colorFromRGB(255, 215, 0); // Gold default
  
  if (coinId == "bitcoin") {
    color = colorFromRGB(247, 147, 26); // Bitcoin orange
    // Draw Bitcoin "B" symbol
    disp->drawRect(2, 2, 8, 12, color);
    disp->drawLine(6, 2, 6, 14, color);
    disp->drawLine(2, 8, 8, 8, color);
  }
  else if (coinId == "ethereum") {
    color = colorFromRGB(98, 126, 234); // Ethereum blue
    // Draw Ethereum diamond
    disp->drawLine(6, 2, 6, 14, color);
    disp->drawLine(6, 2, 2, 8, color);
    disp->drawLine(6, 2, 10, 8, color);
    disp->drawLine(2, 8, 6, 14, color);
    disp->drawLine(10, 8, 6, 14, color);
  }
  else if (coinId == "cardano") {
    color = colorFromRGB(0, 51, 173); // Cardano blue
    // Draw circles for Cardano
    disp->drawCircle(6, 6, 3, color);
    disp->drawCircle(6, 10, 2, color);
  }
  else if (coinId == "solana") {
    color = colorFromRGB(0, 255, 163); // Solana green
    // Draw Solana lines
    disp->drawLine(2, 4, 10, 4, color);
    disp->drawLine(2, 8, 10, 8, color);
    disp->drawLine(2, 12, 10, 12, color);
  }
  else if (coinId == "chainlink") {
    color = colorFromRGB(43, 109, 237); // Chainlink blue
    // Draw chain links
    disp->drawRect(2, 4, 4, 4, color);
    disp->drawRect(6, 8, 4, 4, color);
    disp->drawLine(4, 6, 8, 10, color);
  }
  else if (coinId == "polkadot") {
    color = colorFromRGB(230, 1, 122); // Polkadot pink
    // Draw dots pattern
    disp->fillCircle(4, 6, 1, color);
    disp->fillCircle(8, 6, 1, color);
    disp->fillCircle(6, 10, 1, color);
  }
  else if (coinId == "dogecoin") {
    color = colorFromRGB(198, 147, 34); // Doge gold
    // Draw simple dog face
    disp->drawCircle(6, 8, 5, color);
    disp->fillCircle(4, 6, 1, color); // Left eye
    disp->fillCircle(8, 6, 1, color); // Right eye
    disp->drawLine(6, 10, 6, 11, color); // Nose
  }
  else {
    // Generic crypto symbol - diamond
    disp->drawLine(6, 2, 2, 8, color);
    disp->drawLine(6, 2, 10, 8, color);
    disp->drawLine(2, 8, 6, 14, color);
    disp->drawLine(10, 8, 6, 14, color);
  }
}