#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_crypto_ticker/CryptoDisplay.h"
#ifndef CRYPTO_DISPLAY_H
#define CRYPTO_DISPLAY_H

#include <Arduino.h>
#include <ESP32-HUB75-MatrixPanel-I2S-DMA.h>
#include <time.h>

struct CryptoData {
  String name;
  String symbol;
  float price;
  float change24h;
  float volume;
  int marketCapRank;
  float ath;
  bool isAtATH;
  bool isValid;
  unsigned long lastUpdate;
  float priceHistory[24]; // Store 24 hours of price data for sparkline
  int historyIndex;
  bool hasHistory;
};

class CryptoDisplay {
public:
  CryptoDisplay();
  void init(MatrixPanel_I2S_DMA* display);
  void setCryptoData(const String& coinId, float price, float change24h, float volume = 0, int rank = 0, float ath = 0);
  void displayCurrentCrypto();
  void update();
  void setScrollText(const String& text);
  void setBrightness(int brightness);
  void setPower(bool power);
  void setScrollDirection(const String& direction);
  void setFiatCurrency(const String& currency);
  void setShowSparkline(bool show);
  void setShowRank(bool show);
  void setShowVolume(bool show);

private:
  MatrixPanel_I2S_DMA* disp;
  CryptoData currentCrypto;
  String scrollText;
  int scrollX;
  int scrollY;
  unsigned long lastScrollUpdate;
  unsigned long lastDisplayUpdate;
  bool showClock;
  bool displayPower;
  String scrollDirection;
  String fiatCurrency;
  String fiatSymbol;
  bool showSparkline;
  bool showRank;
  bool showVolume;
  
  void drawCryptoInfo();
  void drawScrollingText();
  void drawClock();
  void drawSparkline();
  void drawErrorScreen(const String& message);
  void drawCryptoIcon(const String& coinId);
  void updatePriceHistory(float price);
  uint16_t colorFromRGB(uint8_t r, uint8_t g, uint8_t b);
  String formatPrice(float price);
  String formatVolume(float volume);
  String getCryptoSymbol(const String& coinId);
  String getFiatSymbol(const String& currency);
  uint16_t getPriceChangeColor(float change);
  void scrollUp();
  void scrollDown();
  void scrollLeft();
  void scrollRight();
};

#endif