#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_crypto_ticker/README.md"
# ESP32 LED Matrix MQTT Display

A comprehensive ESP32-based LED matrix display system with MQTT integration, combining features from the original Waveshare ESPHome samples into a standalone Arduino IDE project.

## Features

### Core Functionality
- **64x32 RGB LED Matrix Display** - Compatible with Waveshare P2.5 panels
- **MQTT Integration** - Subscribe to temperature sensors and control commands
- **Web Interface** - Browser-based control panel for configuration
- **Multiple Display Pages** - Temperature, clock, scrolling text, and combined views
- **Auto Page Rotation** - Cycles through different display modes

### Sample1.yaml Features (Temperature Display)
- ✅ **Temperature Display** - Shows living room temperature from MQTT
- ✅ **Purple Color Theme** - Matches original ESPHome styling
- ✅ **Auto-refresh** - Updates when new temperature data arrives
- ✅ **Error Handling** - Shows "--.-°C" when no data available

### Sample2.yaml Features (Advanced Display)
- ✅ **Scrolling Text** - Customizable marquee text display
- ✅ **Clock Display** - Real-time clock with timezone support (Europe/Dublin)
- ✅ **Background Support** - Framework for background images (URL configurable)
- ✅ **Web Server** - Built-in configuration interface
- ✅ **Combined View** - Shows clock, temperature, and scrolling text together
- ✅ **Color Coding** - Red clock, yellow scrolling text, green temperature

## Hardware Setup

### Waveshare P2.5 64x32 RGB LED Matrix Pin Connections
```
ESP32 Pin → Matrix Pin
19 → R1
13 → G1  
18 → B1
5 → R2
12 → G2
17 → B2
16 → A
14 → B
4 → C
27 → D
25 → E
26 → LAT
15 → OE
2 → CLK
```

### Required Libraries
- ESP32-HUB75-MatrixPanel-I2S-DMA
- PubSubClient (MQTT)
- ArduinoJson
- Adafruit GFX Library

## Configuration

### WiFi & MQTT Settings
Edit these defines in the main .ino file:
```cpp
#define WIFI_SSID "your_wifi_ssid"
#define WIFI_PASSWORD "your_wifi_password"
#define MQTT_HOST "*************"
#define MQTT_PORT 1883
```

### MQTT Topics
- `sensor/living_room/temperature` - Temperature input (float)
- `display/text` - Scrolling text message (string)
- `display/brightness` - Brightness control (0-255)
- `display/power` - Power on/off (on/off)
- `display/show_clock` - Clock visibility (on/off)
- `display/status` - Device status (JSON)

## File Structure
```
esp32_ledmatrix_mqtt/
├── esp32_ledmatrix_mqtt.ino    # Main Arduino sketch
├── DisplayPages.h              # Display page class definitions
├── DisplayPages.cpp            # Display page implementations
└── data/
    └── index.html             # Web interface
```

## Usage

### Web Interface
1. Connect to the ESP32's IP address in your browser
2. Use the control panel to:
   - Turn display on/off
   - Adjust brightness (0-255)
   - Set scrolling text message
   - Toggle clock display
   - View system status

### MQTT Control
Send messages to the MQTT topics to control the display:
```bash
# Set temperature
mosquitto_pub -h MQTT_HOST -t "sensor/living_room/temperature" -m "23.5"

# Update scrolling text
mosquitto_pub -h MQTT_HOST -t "display/text" -m "Hello World!"

# Set brightness
mosquitto_pub -h MQTT_HOST -t "display/brightness" -m "128"

# Power control
mosquitto_pub -h MQTT_HOST -t "display/power" -m "on"
```

## Display Pages

### 1. Combined Page (Default)
- Clock display at top (HH:MM)
- Temperature reading
- Scrolling text at bottom
- Auto-updates all elements

### 2. Temperature Page
- Large temperature display
- "Living room:" label
- Purple color theme (matching sample1.yaml)

### 3. Clock Page
- Full-screen clock display (HH:MM:SS)
- Date display (DD Mon)
- Red time, cyan date

### 4. Scrolling Text Page
- Full-width scrolling text
- Optional clock overlay
- Yellow text color

## Technical Details

### Time Synchronization
- Uses SNTP for time sync
- Configured for Europe/Dublin timezone
- Supports DST transitions

### Memory Management
- Efficient display buffering
- Minimal memory footprint
- Optimized for ESP32 constraints

### Error Handling
- MQTT reconnection logic
- WiFi connection recovery
- Graceful sensor data handling

## Differences from ESPHome

This Arduino implementation provides:
- **No Home Assistant dependency** - Standalone MQTT operation
- **Modular code structure** - Separate .h/.cpp files
- **Extended web interface** - More comprehensive than ESPHome web_server
- **Flexible page system** - Easy to add new display modes
- **Direct hardware control** - No ESPHome abstraction layer

## Future Enhancements

- [ ] Background image loading via HTTP
- [ ] Additional sensor inputs
- [ ] Weather data integration
- [ ] Animation effects
- [ ] Configuration persistence
- [ ] OTA updates

## Installation

1. Install required libraries through Arduino Library Manager
2. Upload the sketch to your ESP32
3. Upload the data folder to SPIFFS
4. Configure WiFi and MQTT settings
5. Connect your LED matrix panel
6. Access the web interface and start controlling your display!

## License

This project is provided as-is for educational and personal use. Based on the original Waveshare examples but significantly extended and restructured for Arduino IDE.