#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_crypto_ticker/.vscode/arduino.json"
{
  "port": "",
  "configuration": "UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default",
  "output": "build",
  "board": "esp32:esp32:esp32s3",
  "programmer": "",
  "useProgrammer": false,
  "optimize_for_debug": false,
  "configurationRequired": true,
  "monitorPortSettings": {
    "port": "",
    "baudRate": 115200,
    "lineEnding": "\r\n",
    "dataBits": 8,
    "parity": "none",
    "stopBits": "one"
  }
}