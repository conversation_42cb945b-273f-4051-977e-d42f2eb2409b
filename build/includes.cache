[{"Sourcefile": null, "Include": "", "Includepath": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32"}, {"Sourcefile": null, "Include": "", "Includepath": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_crypto_ticker/build/sketch/esp32_crypto_ticker.ino.cpp", "Include": "WiFi.h", "Includepath": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_crypto_ticker/build/sketch/esp32_crypto_ticker.ino.cpp", "Include": "Network.h", "Includepath": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_crypto_ticker/build/sketch/esp32_crypto_ticker.ino.cpp", "Include": "HTTPClient.h", "Includepath": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_crypto_ticker/build/sketch/esp32_crypto_ticker.ino.cpp", "Include": "NetworkClientSecure.h", "Includepath": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_crypto_ticker/build/sketch/esp32_crypto_ticker.ino.cpp", "Include": "ArduinoJson.h", "Includepath": "/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_crypto_ticker/build/sketch/esp32_crypto_ticker.ino.cpp", "Include": "ESP32-HUB75-MatrixPanel-I2S-DMA.h", "Includepath": "/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_crypto_ticker/build/sketch/esp32_crypto_ticker.ino.cpp", "Include": "Adafruit_GFX.h", "Includepath": "/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_crypto_ticker/build/sketch/esp32_crypto_ticker.ino.cpp", "Include": "Adafruit_I2CDevice.h", "Includepath": "/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_crypto_ticker/build/sketch/esp32_crypto_ticker.ino.cpp", "Include": "Wire.h", "Includepath": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_crypto_ticker/build/sketch/esp32_crypto_ticker.ino.cpp", "Include": "SPI.h", "Includepath": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_crypto_ticker/build/sketch/esp32_crypto_ticker.ino.cpp", "Include": "PubSubClient.h", "Includepath": "/home/<USER>/Documents/Arduino/libraries/PubSubClient/src"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_crypto_ticker/build/sketch/esp32_crypto_ticker.ino.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_crypto_ticker/build/sketch/CryptoDisplay.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/AP.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/STA.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFi.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiAP.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiGeneric.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiMulti.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiSTA.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiScan.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkClient.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkEvents.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkInterface.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkManager.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkServer.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkUdp.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src/NetworkClientSecure.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src/ssl_client.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src/ESP32-HUB75-MatrixPanel-I2S-DMA.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src/ESP32-HUB75-MatrixPanel-leddrivers.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src/platforms/esp32/esp32_i2s_parallel_dma.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src/platforms/esp32c6/dma_parallel_io.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src/platforms/esp32s3/gdma_lcd_parallel16.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library/Adafruit_GFX.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library/Adafruit_GrayOLED.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library/Adafruit_SPITFT.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library/glcdfont.c", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO/Adafruit_BusIO_Register.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO/Adafruit_GenericDevice.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO/Adafruit_I2CDevice.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO/Adafruit_SPIDevice.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src/Wire.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src/SPI.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/PubSubClient/src/PubSubClient.cpp", "Include": "", "Includepath": null}]