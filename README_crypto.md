# ESP32 Crypto Ticker

ESP32-based cryptocurrency price ticker using HUB75 LED matrix display. This project combines the crypto ticker functionality from rgb-matrix-crypto-ticker with the ESP32-HUB75-MatrixPanel-I2S-DMA library.

## Features

- Real-time cryptocurrency price display
- Multiple coin support (Bitcoin, Ethereum, Cardano, Solana)
- 24-hour price change indicators with color coding
- Scrolling text display with price information
- Built-in clock display
- Auto-cycling between different cryptocurrencies
- WiFi connectivity for API data fetching
- Configurable update intervals

## Hardware Requirements

- ESP32 development board
- HUB75 RGB LED Matrix (64x32 recommended)
- Proper power supply for the LED matrix

## Pin Configuration

The default pin configuration is set for Waveshare P2.5 compatibility:

```cpp
#define R1_PIN 19
#define G1_PIN 13
#define B1_PIN 18
#define R2_PIN 5
#define G2_PIN 12
#define B2_PIN 17
#define A_PIN 16
#define B_PIN 14
#define C_PIN 4
#define D_PIN 27
#define E_PIN 25
#define LAT_PIN 26
#define OE_PIN 15
#define CLK_PIN 2
```

## Libraries Required

- ESP32-HUB75-MatrixPanel-I2S-DMA
- ArduinoJson
- Adafruit_GFX
- WiFi (ESP32 core)
- HTTPClient (ESP32 core)

## Configuration

1. Set your WiFi credentials:
```cpp
#define WIFI_SSID "your_wifi_ssid"
#define WIFI_PASSWORD "your_wifi_password"
```

2. Configure cryptocurrencies to display:
```cpp
String cryptoCurrencies[] = {"bitcoin", "ethereum", "cardano", "solana"};
```

3. Adjust update intervals:
```cpp
#define UPDATE_INTERVAL 60000  // 60 seconds for API updates
#define DISPLAY_UPDATE_INTERVAL 5000  // 5 seconds for display cycling
```

## API Integration

Uses CoinGecko API for real-time price data:
- Endpoint: `https://api.coingecko.com/api/v3/simple/price`
- Includes current price and 24-hour change percentage
- No API key required for basic usage

## Display Features

- **Price Display**: Shows current price in USD with appropriate decimal places
- **Change Indicator**: Color-coded 24h change (green for positive, red for negative)
- **Scrolling Text**: Continuously scrolling ticker with price information
- **Clock**: Time display in HH:MM format
- **Auto-cycling**: Rotates through configured cryptocurrencies

## Installation

1. Install required libraries through Arduino Library Manager
2. Set your WiFi credentials in the code
3. Configure desired cryptocurrencies
4. Upload to ESP32
5. Connect to HUB75 matrix according to pin configuration

## Usage

The device will:
1. Connect to WiFi on startup
2. Synchronize time via NTP
3. Begin fetching cryptocurrency data
4. Display prices with auto-cycling between coins
5. Update data every 60 seconds
6. Cycle display every 5 seconds

## Customization

- **Add more cryptocurrencies**: Modify the `cryptoCurrencies` array with CoinGecko coin IDs
- **Change display layout**: Modify the drawing functions in `CryptoDisplay.cpp`
- **Adjust colors**: Update the color values in the display functions
- **Panel size**: Modify `PANEL_RES_X` and `PANEL_RES_Y` for different matrix sizes

## Troubleshooting

- Ensure proper power supply for the LED matrix
- Check WiFi connection if data isn't updating
- Verify pin connections match the configuration
- Monitor serial output for debug information

## Credits

Based on the original rgb-matrix-crypto-ticker project and adapted for ESP32 with HUB75 matrix displays.