<!DOCTYPE html>
<html>
<head>
    <title>ESP32 Crypto Ticker</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            color: #ffffff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #00ff88;
            text-shadow: 0 0 20px #00ff88;
            font-size: 2.5em;
            font-weight: bold;
        }

        .crypto-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .crypto-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .crypto-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 255, 136, 0.2);
        }

        .crypto-symbol {
            font-size: 1.8em;
            font-weight: bold;
            color: #00ff88;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .crypto-price {
            font-size: 2.2em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #ffffff;
        }

        .crypto-change {
            font-size: 1.2em;
            font-weight: bold;
            padding: 5px 15px;
            border-radius: 20px;
            display: inline-block;
        }

        .positive { background: rgba(0, 255, 0, 0.2); color: #00ff00; }
        .negative { background: rgba(255, 0, 0, 0.2); color: #ff4444; }

        .status-panel {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
        }

        .status-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-label {
            font-size: 12px;
            color: #aaa;
            margin-bottom: 5px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .status-value {
            font-size: 18px;
            font-weight: bold;
            color: #00ff88;
        }

        .control-section {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .section-title {
            color: #00ff88;
            margin-bottom: 15px;
            font-size: 18px;
            border-bottom: 2px solid #00ff88;
            padding-bottom: 5px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            color: #ccc;
            font-weight: 500;
        }

        select, input {
            width: 100%;
            padding: 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            color: #fff;
            font-size: 14px;
            backdrop-filter: blur(5px);
        }

        select:focus, input:focus {
            outline: none;
            border-color: #00ff88;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
        }

        .btn {
            background: linear-gradient(135deg, #00aa88, #008866);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn:hover {
            background: linear-gradient(135deg, #00cc99, #00aa88);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 255, 136, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff4757, #ff3838);
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #ff6b7a, #ff4757);
        }

        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .slider-container {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .slider {
            flex: 1;
            height: 8px;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.2);
            outline: none;
            -webkit-appearance: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #00ff88;
            cursor: pointer;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
        }

        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 12px;
            text-transform: uppercase;
            backdrop-filter: blur(10px);
        }

        .connected {
            background: rgba(0, 170, 136, 0.8);
            color: white;
            box-shadow: 0 0 15px rgba(0, 170, 136, 0.5);
        }

        .disconnected {
            background: rgba(255, 71, 87, 0.8);
            color: white;
            box-shadow: 0 0 15px rgba(255, 71, 87, 0.5);
        }

        .success-message, .error-message {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
            backdrop-filter: blur(10px);
        }

        .success-message {
            background: rgba(0, 170, 136, 0.2);
            color: #00ff88;
            border: 1px solid #00aa88;
        }

        .error-message {
            background: rgba(255, 71, 87, 0.2);
            color: #ff4757;
            border: 1px solid #ff4757;
        }

        .last-update {
            color: #999;
            font-size: 0.9em;
            text-align: center;
            margin-top: 10px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .button-group {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
            }
            
            .slider-container {
                flex-direction: column;
                align-items: stretch;
            }

            h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>₿ ESP32 Crypto Ticker</h1>
        
        <div class="connection-status disconnected" id="connectionStatus">
            Connecting...
        </div>

        <!-- Crypto Display -->
        <div class="crypto-grid" id="cryptoGrid">
            <!-- Dynamic crypto cards will be inserted here -->
        </div>

        <!-- Status Panel -->
        <div class="status-panel">
            <h2 class="section-title">System Status</h2>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-label">Current Coin</div>
                    <div id="current-coin" class="status-value">BTC</div>
                </div>
                <div class="status-item">
                    <div class="status-label">Power</div>
                    <div id="power-status" class="status-value">ON</div>
                </div>
                <div class="status-item">
                    <div class="status-label">Brightness</div>
                    <div id="brightness-value" class="status-value">80</div>
                </div>
                <div class="status-item">
                    <div class="status-label">IP Address</div>
                    <div id="ip-address" class="status-value">---.---.---.---</div>
                </div>
            </div>
            <div class="last-update">
                Last Update: <span id="lastUpdate">Never</span>
            </div>
        </div>

        <!-- Power Control -->
        <div class="control-section">
            <h3 class="section-title">Display Control</h3>
            <div class="button-group">
                <button class="btn" onclick="setPower('on')">Turn ON</button>
                <button class="btn btn-danger" onclick="setPower('off')">Turn OFF</button>
            </div>
        </div>

        <!-- Brightness Control -->
        <div class="control-section">
            <h3 class="section-title">Brightness Control</h3>
            <div class="slider-container">
                <label>0</label>
                <input type="range" class="slider" id="brightnessSlider" 
                       min="0" max="255" value="80" 
                       oninput="updateBrightness(this.value)">
                <label>255</label>
            </div>
            <div style="text-align: center; margin-top: 10px;">
                <span id="brightnessDisplay">80</span>
            </div>
        </div>

        <!-- Crypto Configuration -->
        <div class="control-section">
            <h3 class="section-title">Cryptocurrency Settings</h3>
            <div class="form-group">
                <label for="cryptoSelect">Add Cryptocurrency:</label>
                <select id="cryptoSelect">
                    <option value="">Select a cryptocurrency...</option>
                    <option value="bitcoin">Bitcoin (BTC)</option>
                    <option value="ethereum">Ethereum (ETH)</option>
                    <option value="cardano">Cardano (ADA)</option>
                    <option value="solana">Solana (SOL)</option>
                    <option value="chainlink">Chainlink (LINK)</option>
                    <option value="polkadot">Polkadot (DOT)</option>
                    <option value="dogecoin">Dogecoin (DOGE)</option>
                    <option value="shiba-inu">Shiba Inu (SHIB)</option>
                    <option value="avalanche-2">Avalanche (AVAX)</option>
                    <option value="polygon">Polygon (MATIC)</option>
                </select>
            </div>
            <div class="form-group">
                <label for="updateInterval">Update Interval (seconds):</label>
                <input type="number" id="updateInterval" value="60" min="30" max="300">
            </div>
            <button class="btn" onclick="updateSettings()">Update Settings</button>
        </div>

        <div id="messageContainer"></div>
    </div>

    <script>
        let cryptoData = {};
        let currentSettings = {
            power: true,
            brightness: 80,
            updateInterval: 60,
            currencies: ['bitcoin', 'ethereum', 'cardano', 'solana']
        };

        // Mock crypto data for demo (replace with actual API calls)
        const mockCryptoData = {
            bitcoin: { symbol: 'BTC', price: 43250.75, change: 2.45 },
            ethereum: { symbol: 'ETH', price: 2685.30, change: -1.23 },
            cardano: { symbol: 'ADA', price: 0.4821, change: 5.67 },
            solana: { symbol: 'SOL', price: 98.47, change: -0.89 }
        };

        function formatPrice(price) {
            if (price >= 1000) {
                return '$' + price.toLocaleString(undefined, {minimumFractionDigits: 0, maximumFractionDigits: 0});
            } else if (price >= 1) {
                return '$' + price.toFixed(2);
            } else {
                return '$' + price.toFixed(6);
            }
        }

        function updateCryptoDisplay() {
            const grid = document.getElementById('cryptoGrid');
            grid.innerHTML = '';

            Object.entries(mockCryptoData).forEach(([id, data]) => {
                const card = document.createElement('div');
                card.className = 'crypto-card';
                
                const changeClass = data.change >= 0 ? 'positive' : 'negative';
                const changeSymbol = data.change >= 0 ? '+' : '';
                
                card.innerHTML = `
                    <div class="crypto-symbol">${data.symbol}</div>
                    <div class="crypto-price">${formatPrice(data.price)}</div>
                    <div class="crypto-change ${changeClass}">
                        ${changeSymbol}${data.change.toFixed(2)}%
                    </div>
                `;
                
                grid.appendChild(card);
            });

            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
        }

        // Show message to user
        function showMessage(text, isError = false) {
            const container = document.getElementById('messageContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = isError ? 'error-message' : 'success-message';
            messageDiv.textContent = text;
            
            container.appendChild(messageDiv);
            
            setTimeout(() => {
                if (container.contains(messageDiv)) {
                    container.removeChild(messageDiv);
                }
            }, 3000);
        }

        // Set power state
        function setPower(state) {
            currentSettings.power = (state === 'on');
            document.getElementById('power-status').textContent = state.toUpperCase();
            showMessage(`Display ${state.toUpperCase()}`);
            
            // Here you would make an actual API call to the ESP32
            console.log('Setting power to:', state);
        }

        // Update brightness
        function updateBrightness(value) {
            currentSettings.brightness = parseInt(value);
            document.getElementById('brightnessDisplay').textContent = value;
            document.getElementById('brightness-value').textContent = value;
            
            // Here you would make an actual API call to the ESP32
            console.log('Setting brightness to:', value);
        }

        // Update settings
        function updateSettings() {
            const interval = document.getElementById('updateInterval').value;
            const crypto = document.getElementById('cryptoSelect').value;
            
            if (interval) {
                currentSettings.updateInterval = parseInt(interval);
            }
            
            if (crypto && !currentSettings.currencies.includes(crypto)) {
                currentSettings.currencies.push(crypto);
            }
            
            showMessage('Settings updated successfully!');
            console.log('Updated settings:', currentSettings);
        }

        // Initialize the page
        function initialize() {
            updateCryptoDisplay();
            
            // Set initial values
            document.getElementById('power-status').textContent = 'ON';
            document.getElementById('brightness-value').textContent = '80';
            document.getElementById('current-coin').textContent = 'BTC';
            document.getElementById('ip-address').textContent = '*************';
            
            // Connection status
            const statusEl = document.getElementById('connectionStatus');
            statusEl.className = 'connection-status connected';
            statusEl.textContent = 'Connected';
            
            // Update crypto data periodically
            setInterval(() => {
                // Simulate price changes
                Object.keys(mockCryptoData).forEach(id => {
                    const data = mockCryptoData[id];
                    const changePercent = (Math.random() - 0.5) * 0.1; // ±5% max change
                    data.price *= (1 + changePercent);
                    data.change = (Math.random() - 0.5) * 10; // Random change between -5% and +5%
                });
                updateCryptoDisplay();
            }, 5000); // Update every 5 seconds for demo
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initialize);
    </script>
</body>
</html>